import { cn } from "@/lib/utils";

interface StatusBadgeProps {
  status: "low" | "moderate" | "high" | "full" | "closed";
  children: React.ReactNode;
  className?: string;
}

const statusConfig = {
  low: {
    className: "bg-success-light text-success border-success/20",
    label: "Nízké využití",
  },
  moderate: {
    className: "bg-warning-light text-warning border-warning/20", 
    label: "Střední využití",
  },
  high: {
    className: "bg-destructive-light text-destructive border-destructive/20",
    label: "Vysoké využití", 
  },
  full: {
    className: "bg-destructive text-destructive-foreground border-destructive",
    label: "Plně obsazeno",
  },
  closed: {
    className: "bg-muted text-muted-foreground border-border",
    label: "Zavřeno",
  },
};

const StatusBadge = ({ status, children, className }: StatusBadgeProps) => {
  const config = statusConfig[status];
  
  return (
    <div
      className={cn(
        "inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium border transition-smooth",
        config.className,
        className
      )}
    >
      {children}
    </div>
  );
};

export default StatusBadge;