import { useEffect, useState } from "react";
import { Refresh<PERSON><PERSON>, MapPin, Clock, Car, TrendingUp, Calendar } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts";
import StatusBadge from "@/components/StatusBadge";
import { cn } from "@/lib/utils";
import parkingData from "@/mockData/parking.json";
import parkingHistory from "@/mockData/parkingHistory.json";
import parkingPrediction from "@/mockData/parkingPrediction.json";

interface ParkingSpot {
  name: string;
  location: string;
  capacity: number;
  available: number;
  occupancy: number;
  last_updated: string;
  type: string;
}

const getOccupancyStatus = (occupancy: number) => {
  if (occupancy >= 0.9) return "full";
  if (occupancy >= 0.6) return "high";
  if (occupancy >= 0.3) return "moderate";
  return "low";
};

const getOccupancyColor = (occupancy: number) => {
  if (occupancy >= 0.9) return "text-destructive";
  if (occupancy >= 0.6) return "text-warning";
  if (occupancy >= 0.3) return "text-warning";
  return "text-success";
};

const Parking = () => {
  const [parking, setParking] = useState<ParkingSpot[]>(parkingData.data);
  const [history] = useState(parkingHistory.data);
  const [predictions] = useState(parkingPrediction.data);
  const [selectedLocation, setSelectedLocation] = useState("Parkoviště Senovážné nám.");
  const [selectedDay, setSelectedDay] = useState(() => {
    const days = ['neděle', 'pondělí', 'úterý', 'středa', 'čtvrtek', 'pátek', 'sobota'];
    return days[new Date().getDay()];
  });
  const [lastUpdated, setLastUpdated] = useState(new Date());
  const [isRefreshing, setIsRefreshing] = useState(false);

  const refreshData = async () => {
    setIsRefreshing(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setLastUpdated(new Date());
    setIsRefreshing(false);
  };

  useEffect(() => {
    // Auto-refresh every 2 minutes
    const interval = setInterval(refreshData, 120000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold text-foreground mb-2">
              Parkování v České Budějovice
            </h1>
            <p className="text-muted-foreground">
              Aktuální obsazenost parkovišť v reálném čase
            </p>
          </div>
          <Button
            onClick={refreshData}
            disabled={isRefreshing}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <RefreshCw className={cn("h-4 w-4", isRefreshing && "animate-spin")} />
            Obnovit
          </Button>
        </div>
        
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Clock className="h-4 w-4" />
          Poslední aktualizace: {lastUpdated.toLocaleTimeString("cs-CZ")}
        </div>
      </div>

      {/* Parking Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {parking.map((spot, index) => (
          <Card key={index} className="relative overflow-hidden shadow-civic hover:shadow-lg transition-smooth">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-lg font-semibold mb-1">
                    {spot.name}
                  </CardTitle>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <MapPin className="h-4 w-4 mr-1" />
                    {spot.location}
                  </div>
                </div>
                <StatusBadge status={getOccupancyStatus(spot.occupancy)}>
                  {Math.round((1 - spot.occupancy) * 100)}% volné
                </StatusBadge>
              </div>
            </CardHeader>
            
            <CardContent>
              {/* Occupancy Bar */}
              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm text-muted-foreground">Obsazenost</span>
                  <span className={cn("text-sm font-medium", getOccupancyColor(spot.occupancy))}>
                    {Math.round(spot.occupancy * 100)}%
                  </span>
                </div>
                <div className="w-full bg-muted rounded-full h-2">
                  <div
                    className={cn(
                      "h-2 rounded-full transition-smooth",
                      spot.occupancy >= 0.9 ? "bg-destructive" :
                      spot.occupancy >= 0.6 ? "bg-warning" :
                      spot.occupancy >= 0.3 ? "bg-warning" :
                      "bg-success"
                    )}
                    style={{ width: `${spot.occupancy * 100}%` }}
                  />
                </div>
              </div>

              {/* Stats */}
              <div className="space-y-2">
                <div className="flex justify-between items-center text-sm">
                  <span className="text-muted-foreground">Volná místa:</span>
                  <span className="font-medium text-success">{spot.available}</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-muted-foreground">Celková kapacita:</span>
                  <span className="font-medium">{spot.capacity}</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-muted-foreground">Typ parkoviště:</span>
                  <span className="font-medium">
                    {spot.type === "street" ? "Ulice" : "Garáž"}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Historical Data and Predictions */}
      <Card className="mb-8 shadow-civic">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Analýza a předpovědi
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="history" className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-6">
              <TabsTrigger value="history">Historie obsazenosti</TabsTrigger>
              <TabsTrigger value="prediction">Předpovědi</TabsTrigger>
            </TabsList>
            
            <TabsContent value="history">
               <div className="mb-4 flex flex-col md:flex-row gap-4">
                <Select value={selectedLocation} onValueChange={setSelectedLocation}>
                  <SelectTrigger className="w-full md:w-80">
                    <SelectValue placeholder="Vyberte parkoviště" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.keys(history.locations).map((location) => (
                      <SelectItem key={location} value={location}>
                        {location}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select value={selectedDay} onValueChange={setSelectedDay}>
                  <SelectTrigger className="w-full md:w-48">
                    <SelectValue placeholder="Vyberte den" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pondělí">Pondělí</SelectItem>
                    <SelectItem value="úterý">Úterý</SelectItem>
                    <SelectItem value="středa">Středa</SelectItem>
                    <SelectItem value="čtvrtek">Čtvrtek</SelectItem>
                    <SelectItem value="pátek">Pátek</SelectItem>
                    <SelectItem value="sobota">Sobota</SelectItem>
                    <SelectItem value="neděle">Neděle</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="h-80 w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={history.locations[selectedLocation]?.days?.[selectedDay] || []}>
                    <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                    <XAxis 
                      dataKey="time" 
                      tick={{ fontSize: 12 }}
                      tickLine={{ stroke: 'hsl(var(--muted-foreground))' }}
                    />
                    <YAxis 
                      tick={{ fontSize: 12 }}
                      tickLine={{ stroke: 'hsl(var(--muted-foreground))' }}
                      domain={[0, 1]}
                      tickFormatter={(value) => `${Math.round(value * 100)}%`}
                    />
                    <Tooltip 
                      formatter={(value: number, name) => [
                        name === 'occupancy' ? `${Math.round(value * 100)}%` : `${value} míst`,
                        name === 'occupancy' ? 'Obsazenost' : 'Volná místa'
                      ]}
                      labelFormatter={(label) => `Čas: ${label}`}
                      contentStyle={{
                        backgroundColor: 'hsl(var(--card))',
                        border: '1px solid hsl(var(--border))',
                        borderRadius: '8px'
                      }}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="occupancy" 
                      stroke="hsl(var(--primary))" 
                      strokeWidth={3}
                      dot={{ r: 4, fill: 'hsl(var(--primary))' }}
                      activeDot={{ r: 6, fill: 'hsl(var(--primary))' }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
              
              <div className="mt-4 p-4 bg-muted/50 rounded-lg">
                <h4 className="font-semibold mb-2">Týdenní vzorce:</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Nejrušnější dny:</span>
                    <p className="text-muted-foreground">Pátek, Sobota</p>
                  </div>
                  <div>
                    <span className="font-medium">Špičkové hodiny:</span>
                    <p className="text-muted-foreground">09:00-11:00, 14:00-16:00</p>
                  </div>
                  <div>
                    <span className="font-medium">Nejklidnější:</span>
                    <p className="text-muted-foreground">Neděle ráno</p>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="prediction">
              <div className="mb-4 flex flex-col md:flex-row gap-4">
                <Select value={selectedLocation} onValueChange={setSelectedLocation}>
                  <SelectTrigger className="w-full md:w-80">
                    <SelectValue placeholder="Vyberte parkoviště" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.keys(predictions.predictions).map((location) => (
                      <SelectItem key={location} value={location}>
                        {location}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select value={selectedDay} onValueChange={setSelectedDay}>
                  <SelectTrigger className="w-full md:w-48">
                    <SelectValue placeholder="Vyberte den" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pondělí">Pondělí</SelectItem>
                    <SelectItem value="úterý">Úterý</SelectItem>
                    <SelectItem value="středa">Středa</SelectItem>
                    <SelectItem value="čtvrtek">Čtvrtek</SelectItem>
                    <SelectItem value="pátek">Pátek</SelectItem>
                    <SelectItem value="sobota">Sobota</SelectItem>
                    <SelectItem value="neděle">Neděle</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="h-80 w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={predictions.predictions[selectedLocation]?.days?.[selectedDay] || []}>
                    <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                    <XAxis 
                      dataKey="time" 
                      tick={{ fontSize: 12 }}
                      tickLine={{ stroke: 'hsl(var(--muted-foreground))' }}
                    />
                    <YAxis 
                      tick={{ fontSize: 12 }}
                      tickLine={{ stroke: 'hsl(var(--muted-foreground))' }}
                      domain={[0, 1]}
                      tickFormatter={(value) => `${Math.round(value * 100)}%`}
                    />
                    <Tooltip 
                      formatter={(value: number, name) => [
                        name === 'predicted_occupancy' ? `${Math.round(value * 100)}%` : `${value} míst`,
                        name === 'predicted_occupancy' ? 'Předpokládaná obsazenost' : 'Předpokládaná volná místa'
                      ]}
                      labelFormatter={(label) => `Čas: ${label}`}
                      contentStyle={{
                        backgroundColor: 'hsl(var(--card))',
                        border: '1px solid hsl(var(--border))',
                        borderRadius: '8px'
                      }}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="predicted_occupancy" 
                      stroke="hsl(var(--secondary))" 
                      strokeWidth={3}
                      strokeDasharray="5 5"
                      dot={{ r: 4, fill: 'hsl(var(--secondary))' }}
                      activeDot={{ r: 6, fill: 'hsl(var(--secondary))' }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
              
              {/* Recommendations */}
              <div className="mt-4 space-y-3">
                <h4 className="font-semibold">Doporučení pro {selectedLocation}:</h4>
                {predictions.recommendations.find(rec => rec.location === selectedLocation) && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-3 bg-success-light rounded-lg border border-success/20">
                      <div className="flex items-center gap-2 mb-1">
                        <Calendar className="h-4 w-4 text-success" />
                        <span className="font-medium text-success">Nejlepší časy</span>
                      </div>
                      <p className="text-sm text-success/80">
                        {predictions.recommendations.find(rec => rec.location === selectedLocation)?.best_times.join(", ")}
                      </p>
                    </div>
                    <div className="p-3 bg-warning-light rounded-lg border border-warning/20">
                      <div className="flex items-center gap-2 mb-1">
                        <Clock className="h-4 w-4 text-warning" />
                        <span className="font-medium text-warning">Tip</span>
                      </div>
                      <p className="text-sm text-warning/80">
                        {predictions.recommendations.find(rec => rec.location === selectedLocation)?.tip}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Legend */}
      <div className="mt-8 p-6 bg-muted/50 rounded-lg">
        <h3 className="text-lg font-semibold mb-4">Vysvětlivky</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="flex items-center gap-2">
            <StatusBadge status="low">Nízké</StatusBadge>
            <span className="text-sm">{'<'} 30% obsazeno</span>
          </div>
          <div className="flex items-center gap-2">
            <StatusBadge status="moderate">Střední</StatusBadge>
            <span className="text-sm">30-60% obsazeno</span>
          </div>
          <div className="flex items-center gap-2">
            <StatusBadge status="high">Vysoké</StatusBadge>
            <span className="text-sm">60-90% obsazeno</span>
          </div>
          <div className="flex items-center gap-2">
            <StatusBadge status="full">Plné</StatusBadge>
            <span className="text-sm">{'>'} 90% obsazeno</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Parking;