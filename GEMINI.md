# Project: Budejo Pulse

## Project Overview

This project is a modern React application for monitoring office occupancy, parking availability, and pool usage. It features real-time data visualization and predictive analytics. The application is built with TypeScript, Vite, and React, and it uses Tailwind CSS for styling with shadcn/ui components.

This is a single-page application (SPA) that uses React Router for client-side routing. The main pages are:

*   **Parking:** Monitors parking availability.
*   **Offices:** Tracks office occupancy.
*   **Pool:** Displays pool usage data.

The application uses TanStack Query (React Query) for managing server state and caching data. It also includes a design system with a dark/light mode and a set of reusable components.

## Building and Running

### Prerequisites

*   Node.js (version 18.0.0 or higher)
*   npm (version 8.0.0 or higher) or yarn

### Environment Management with mise

This project uses [mise](httpss://mise.jdx.dev/) to manage the development environment. The `.mise.toml` file specifies the required Node.js and npm versions.

To use it, install mise and then run:

```bash
mise install
```

This will automatically install and configure the correct tool versions for this project.

### Installation

Install dependencies:
```bash
npm install
```

### Development

To start the development server, run:

```bash
npm run dev
```

The application will be available at `http://localhost:8080`.

### Building

To create a production build, run:

```bash
npm run build
```

The optimized static files will be generated in the `dist/` directory.

### Testing

To run tests, use:

```bash
npm test
```

## Development Conventions

*   **TypeScript:** The project is written in TypeScript, and all new code should be strongly typed.
*   **Styling:** The project uses Tailwind CSS with a custom theme defined in `tailwind.config.ts`.
*   **Components:** Reusable UI components are located in the `src/components` directory. The project uses shadcn/ui components, which are in `src/components/ui`.
*   **Routing:** Client-side routing is handled by React Router. The routes are defined in `src/App.tsx`.
*   **State Management:** TanStack Query (React Query) is used for managing server state.
*   **Linting:** The project uses ESLint for code linting. To run the linter, use `npm run lint`.
*   **Path Aliases:** The project uses the `@/` path alias to refer to the `src` directory.
