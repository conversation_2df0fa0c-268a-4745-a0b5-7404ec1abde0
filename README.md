# Office & Facility Management Dashboard

A modern React application for monitoring office occupancy, parking availability, and pool usage with real-time data visualization and predictive analytics.

## 📋 Table of Contents

- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [Development](#development)
- [Building](#building)
- [Testing](#testing)
- [Project Structure](#project-structure)
- [Available Scripts](#available-scripts)
- [Technology Stack](#technology-stack)
- [Features](#features)
- [Troubleshooting](#troubleshooting)
- [Contributing](#contributing)
- [Deployment](#deployment)

## 🔧 Prerequisites

Before you begin, ensure you have the following installed on your system:

- **Node.js** (version 18.0.0 or higher)
- **npm** (version 8.0.0 or higher) or **yarn** (version 1.22.0 or higher)
- **Git** (for version control)

### Installing Node.js

**Using Node Version Manager (recommended):**
```bash
# Install nvm (Unix/macOS)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash

# Install nvm (Windows)
# Download and install from: https://github.com/coreybutler/nvm-windows

# Restart your terminal and install Node.js
nvm install 18
nvm use 18
```

**Direct installation:**
- Download from [nodejs.org](https://nodejs.org/)
- Follow the installation wizard for your operating system

## 🚀 Installation

### 1. Clone the Repository

```bash
# Using HTTPS
git clone https://github.com/your-username/your-repo-name.git

# Using SSH (if you have SSH keys set up)
<NAME_EMAIL>:your-username/your-repo-name.git

# Navigate to the project directory
cd your-repo-name
```

### 2. Install Dependencies

```bash
# Using npm
npm install

# Using yarn (alternative)
yarn install
```

This will install all required dependencies including:
- React 18
- TypeScript
- Vite
- Tailwind CSS
- shadcn/ui components
- React Router
- TanStack Query
- Recharts for data visualization

## 💻 Development

### Starting the Development Server

```bash
# Using npm
npm run dev

# Using yarn
yarn dev
```

The development server will start on `http://localhost:8080` with:
- Hot module replacement (HMR)
- TypeScript compilation
- Tailwind CSS processing
- Auto-refresh on file changes

### Development Features

- **Hot Reload**: Changes are reflected immediately without losing state
- **TypeScript Support**: Full type checking and IntelliSense
- **Component Tagging**: Development-only component identification
- **Path Aliases**: Use `@/` to import from the `src` directory

### Environment Setup

The application runs on port `8080` by default and accepts connections from all interfaces (`::`) for development flexibility.

## 🏗️ Building

### Production Build

```bash
# Create optimized production build
npm run build

# Using yarn
yarn build
```

This will:
- Compile TypeScript to JavaScript
- Bundle and optimize all assets
- Generate static files in the `dist/` directory
- Apply code splitting and tree shaking
- Minify CSS and JavaScript

### Build Output

The build creates:
- `dist/index.html` - Main HTML file
- `dist/assets/` - Optimized JavaScript, CSS, and other assets
- Source maps for debugging (in development builds)

### Preview Production Build

```bash
# Preview the production build locally
npm run preview

# Using yarn
yarn preview
```

## 🧪 Testing

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### Testing Strategy

The project supports:
- **Unit Tests**: Component and utility function testing
- **Integration Tests**: Multi-component interaction testing
- **E2E Tests**: Full user workflow testing

### Test Structure

```
src/
├── __tests__/          # Test files
├── components/
│   └── __tests__/      # Component-specific tests
└── lib/
    └── __tests__/      # Utility function tests
```

### Writing Tests

```typescript
// Example component test
import { render, screen } from '@testing-library/react'
import { StatusBadge } from '../StatusBadge'

test('renders status badge correctly', () => {
  render(<StatusBadge status="available" count={5} />)
  expect(screen.getByText('5')).toBeInTheDocument()
})
```

## 📁 Project Structure

```
project-root/
├── public/                 # Static assets
│   ├── robots.txt         # SEO robots file
│   └── favicon.ico        # Application icon
├── src/
│   ├── components/        # Reusable UI components
│   │   ├── ui/           # shadcn/ui components
│   │   ├── Navigation.tsx # Main navigation
│   │   └── StatusBadge.tsx # Status indicator
│   ├── pages/            # Route components
│   │   ├── Index.tsx     # Home page (redirects to Parking)
│   │   ├── Parking.tsx   # Parking management
│   │   ├── Offices.tsx   # Office occupancy
│   │   ├── Pool.tsx      # Pool usage
│   │   └── NotFound.tsx  # 404 page
│   ├── mockData/         # Development data
│   │   ├── parking.json  # Current parking data
│   │   ├── parkingHistory.json # Historical data
│   │   └── [other mock files]
│   ├── hooks/            # Custom React hooks
│   ├── lib/              # Utility functions
│   ├── App.tsx           # Main application component
│   ├── main.tsx          # Application entry point
│   └── index.css         # Global styles & design tokens
├── tailwind.config.ts    # Tailwind CSS configuration
├── vite.config.ts        # Vite build configuration
├── tsconfig.json         # TypeScript configuration
└── package.json          # Project dependencies & scripts
```

## 📜 Available Scripts

```bash
# Development
npm run dev              # Start development server
npm run build            # Create production build
npm run preview          # Preview production build
npm run lint             # Run ESLint
npm run lint:fix         # Fix ESLint errors automatically

# Testing
npm test                 # Run tests
npm run test:watch       # Run tests in watch mode
npm run test:coverage    # Generate coverage report

# Utilities
npm run type-check       # Run TypeScript compiler check
npm run clean            # Clean build artifacts
```

## 🛠️ Technology Stack

This project is built with modern web technologies:

### Core Framework
- **React 18** - Component-based UI framework with hooks
- **TypeScript** - Type-safe JavaScript with enhanced developer experience
- **Vite** - Fast build tool with hot module replacement

### Styling & UI
- **Tailwind CSS** - Utility-first CSS framework
- **shadcn/ui** - High-quality, accessible React components
- **Lucide React** - Beautiful, customizable icons
- **CSS Variables** - Design system with semantic color tokens

### State Management & Data
- **TanStack Query (React Query)** - Server state management and caching
- **React Router** - Client-side routing
- **Recharts** - Data visualization and charting library

### Development Tools
- **ESLint** - Code linting and style enforcement
- **PostCSS** - CSS processing and optimization
- **Path Aliases** - Clean import statements with `@/` prefix

## ✨ Features

### Dashboard Overview
- **Real-time Monitoring**: Live data updates for all facilities
- **Multi-facility Support**: Parking, offices, and pool management
- **Responsive Design**: Works on desktop, tablet, and mobile devices

### Data Visualization
- **Interactive Charts**: Historical trends and predictions
- **Status Indicators**: Color-coded availability and occupancy
- **Day-by-day Analysis**: Detailed breakdown by weekdays

### User Experience
- **Dark/Light Mode**: Automatic theme switching
- **Intuitive Navigation**: Clean, accessible interface
- **Loading States**: Smooth data fetching experience
- **Error Handling**: Graceful error states and recovery

### Technical Features
- **TypeScript Safety**: Full type coverage for better reliability
- **Component Architecture**: Modular, reusable components
- **Performance Optimized**: Code splitting and lazy loading
- **SEO Ready**: Semantic HTML and meta tags

## 🔧 Troubleshooting

### Common Issues

#### Port Already in Use
```bash
# Kill process on port 8080
lsof -ti:8080 | xargs kill -9

# Or use a different port
npm run dev -- --port 3000
```

#### Node Version Issues
```bash
# Check your Node version
node --version

# Update to Node 18+ using nvm
nvm install 18
nvm use 18
```

#### TypeScript Errors
```bash
# Clear TypeScript cache
npx tsc --build --clean

# Restart TypeScript server in VS Code
Ctrl/Cmd + Shift + P -> "TypeScript: Restart TS Server"
```

#### Build Failures
```bash
# Clear all caches and reinstall
rm -rf node_modules package-lock.json
npm install

# Clear Vite cache
rm -rf dist .vite
npm run build
```

### Performance Issues

#### Slow Development Server
- Ensure you're using Node 18+
- Close unnecessary browser tabs
- Disable browser extensions during development

#### Large Bundle Size
- Use `npm run build -- --analyze` to analyze bundle
- Implement code splitting for large components
- Use dynamic imports for routes

## 🤝 Contributing

### Development Workflow

1. **Fork the repository**
2. **Create a feature branch**
   ```bash
   git checkout -b feature/amazing-feature
   ```
3. **Make your changes**
   - Follow existing code style
   - Add TypeScript types
   - Write tests for new features
4. **Test your changes**
   ```bash
   npm test
   npm run build
   ```
5. **Commit your changes**
   ```bash
   git commit -m "Add amazing feature"
   ```
6. **Push to the branch**
   ```bash
   git push origin feature/amazing-feature
   ```
7. **Open a Pull Request**

### Code Style Guidelines

- Use TypeScript for all new code
- Follow existing component patterns
- Use semantic HTML elements
- Implement responsive design
- Add proper accessibility attributes
- Use the design system tokens

### Testing Requirements

- Write unit tests for utility functions
- Add component tests for UI components
- Ensure accessibility compliance
- Test responsive design

## 🚀 Deployment

### Manual Deployment

#### Static Hosting (Netlify, Vercel, etc.)
```bash
# Build the project
npm run build

# Deploy the dist/ folder to your hosting provider
```

### Environment Variables

For production deployments, ensure all required environment variables are set:
- No environment variables required for basic functionality
- Add API endpoints if connecting to real backends


